#!/usr/bin/env python3
"""
逐步调试脚本
"""

import sys
import traceback

def step1_basic_imports():
    """步骤1: 测试基础包导入"""
    print("=" * 50)
    print("步骤1: 测试基础包导入")
    print("=" * 50)
    
    try:
        print("导入 pandas...")
        import pandas as pd
        print("✅ pandas 导入成功")
        
        print("导入 sqlalchemy...")
        from sqlalchemy import create_engine, text
        print("✅ sqlalchemy 导入成功")
        
        print("导入 datetime...")
        from datetime import datetime, timedelta
        print("✅ datetime 导入成功")
        
        print("导入 logging...")
        import logging
        print("✅ logging 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 基础包导入失败: {e}")
        traceback.print_exc()
        return False

def step2_tushare():
    """步骤2: 测试tushare"""
    print("\n" + "=" * 50)
    print("步骤2: 测试tushare")
    print("=" * 50)
    
    try:
        print("导入 tushare...")
        import tushare as ts
        print("✅ tushare 导入成功")
        
        print("设置 token...")
        TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
        ts.set_token(TUSHARE_TOKEN)
        print("✅ token 设置成功")
        
        print("初始化 pro_api...")
        pro = ts.pro_api()
        print("✅ pro_api 初始化成功")
        
        return True
    except Exception as e:
        print(f"❌ tushare 测试失败: {e}")
        traceback.print_exc()
        return False

def step3_database():
    """步骤3: 测试数据库连接"""
    print("\n" + "=" * 50)
    print("步骤3: 测试数据库连接")
    print("=" * 50)
    
    try:
        from sqlalchemy import create_engine, text
        
        print("创建数据库引擎...")
        DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"
        engine = create_engine(DATABASE_URL)
        print("✅ 数据库引擎创建成功")
        
        print("测试连接...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            row = result.fetchone()
            if row and row[0] == 1:
                print("✅ 数据库连接成功")
                return True
            else:
                print("❌ 数据库连接失败 - 查询结果异常")
                return False
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

def step4_framework_import():
    """步骤4: 测试增量框架导入"""
    print("\n" + "=" * 50)
    print("步骤4: 测试增量框架导入")
    print("=" * 50)

    try:
        print("导入增量处理框架...")
        from 增量处理框架 import IncrementalProcessor, RateLimiter, api_call_with_retry
        print("✅ 增量处理框架导入成功")

        print("测试 RateLimiter...")
        rate_limiter = RateLimiter()
        print("✅ RateLimiter 创建成功")

        print("✅ 增量框架核心组件导入成功")

        return True
    except Exception as e:
        print(f"❌ 增量框架导入失败: {e}")
        traceback.print_exc()
        return False

def step5_processors_import():
    """步骤5: 测试各个处理器导入"""
    print("\n" + "=" * 50)
    print("步骤5: 测试各个处理器导入")
    print("=" * 50)
    
    try:
        print("导入股票日线增量处理器...")
        from 股票日线增量 import StockDailyIncrementalProcessor
        print("✅ 股票日线增量处理器导入成功")
        
        print("导入指数增量处理器...")
        from 指数增量 import IndexIncrementalProcessor
        print("✅ 指数增量处理器导入成功")
        
        print("导入财务数据增量处理器...")
        from 财务数据增量 import FinancialDataIncrementalProcessor
        print("✅ 财务数据增量处理器导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 处理器导入失败: {e}")
        traceback.print_exc()
        return False

def step6_watermark_test():
    """步骤6: 测试水位线功能"""
    print("\n" + "=" * 50)
    print("步骤6: 测试水位线功能")
    print("=" * 50)

    try:
        from 股票日线增量 import StockDailyIncrementalProcessor
        from sqlalchemy import create_engine, text

        print("创建股票日线处理器（用于测试水位线）...")
        processor = StockDailyIncrementalProcessor()

        print("测试水位线更新...")
        test_job = 'debug_test'
        test_date = '20240315'
        processor.update_watermark(test_job, test_date, 100, 'SUCCESS')
        print("✅ 水位线更新成功")

        print("测试水位线读取...")
        watermark = processor.get_last_watermark(test_job)
        if watermark and watermark['last_trade_date'] == test_date:
            print("✅ 水位线读取成功")
        else:
            print(f"❌ 水位线读取失败: {watermark}")
            return False

        print("清理测试数据...")
        DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"
        engine = create_engine(DATABASE_URL)
        with engine.connect() as conn:
            conn.execute(text(f"DELETE FROM incremental_watermarks WHERE job_name = '{test_job}'"))
            conn.commit()
        print("✅ 测试数据清理完成")

        return True
    except Exception as e:
        print(f"❌ 水位线测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 增量功能逐步调试")
    print("=" * 60)
    
    steps = [
        ("基础包导入", step1_basic_imports),
        ("Tushare", step2_tushare),
        ("数据库连接", step3_database),
        ("增量框架导入", step4_framework_import),
        ("处理器导入", step5_processors_import),
        ("水位线功能", step6_watermark_test)
    ]
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print(f"\n🔍 执行步骤 {i}: {step_name}")
        success = step_func()
        
        if not success:
            print(f"\n❌ 步骤 {i} 失败: {step_name}")
            print("调试在此步骤停止。请修复上述错误后重新运行。")
            return False
        else:
            print(f"✅ 步骤 {i} 成功: {step_name}")
    
    print("\n" + "=" * 60)
    print("🎉 所有调试步骤都成功完成！")
    print("增量功能应该可以正常工作了。")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 调试完成，可以运行完整测试:")
        print("python 测试增量功能.py")
    else:
        print("\n❌ 调试失败，请根据上面的错误信息修复问题")
    
    sys.exit(0 if success else 1)
