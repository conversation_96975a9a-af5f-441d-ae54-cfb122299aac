#!/usr/bin/env python3
"""
增量更新调度器
统一管理所有数据源的增量更新
"""

import argparse
import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
try:
    import schedule
except ImportError:
    print("请安装 schedule 包: pip install schedule")
    sys.exit(1)
import time

# 导入各个增量处理器
from 股票日线增量 import run_stock_daily_incremental
from 指数增量 import run_index_incremental
from 财务数据增量 import run_financial_incremental

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('incremental_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IncrementalScheduler:
    """增量更新调度器"""
    
    def __init__(self):
        self.jobs = {
            'stock_daily': {
                'function': run_stock_daily_incremental,
                'description': '股票日线数据增量更新',
                'default_schedule': '09:00',  # 每天9点运行
                'enabled': True
            },
            'index': {
                'function': run_index_incremental,
                'description': '指数数据增量更新',
                'default_schedule': '09:30',  # 每天9点30分运行
                'enabled': True
            },
            'financial': {
                'function': run_financial_incremental,
                'description': '财务数据增量更新',
                'default_schedule': '10:00',  # 每天10点运行
                'enabled': True
            }
        }
    
    def run_single_job(self, job_name: str, force_start_date: str = None, 
                      end_date: str = None) -> bool:
        """运行单个增量任务"""
        if job_name not in self.jobs:
            logger.error(f"Unknown job: {job_name}")
            return False
        
        job_info = self.jobs[job_name]
        if not job_info['enabled']:
            logger.info(f"Job {job_name} is disabled, skipping")
            return True
        
        logger.info(f"Starting {job_info['description']}")
        start_time = datetime.now()
        
        try:
            # 运行增量更新
            records = job_info['function'](
                force_start_date=force_start_date,
                end_date=end_date
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✅ {job_info['description']} completed successfully")
            logger.info(f"   Records processed: {records}")
            logger.info(f"   Duration: {duration:.1f} seconds")
            
            return True
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"❌ {job_info['description']} failed")
            logger.error(f"   Error: {e}")
            logger.error(f"   Duration: {duration:.1f} seconds")
            
            return False
    
    def run_all_jobs(self, force_start_date: str = None, end_date: str = None) -> Dict[str, bool]:
        """运行所有增量任务"""
        logger.info("=" * 60)
        logger.info("Starting incremental update for all data sources")
        logger.info("=" * 60)
        
        results = {}
        total_start_time = datetime.now()
        
        for job_name in self.jobs.keys():
            success = self.run_single_job(job_name, force_start_date, end_date)
            results[job_name] = success
            
            # 任务间添加短暂延迟
            if job_name != list(self.jobs.keys())[-1]:  # 不是最后一个任务
                logger.info("Waiting 30 seconds before next job...")
                time.sleep(30)
        
        total_end_time = datetime.now()
        total_duration = (total_end_time - total_start_time).total_seconds()
        
        # 汇总结果
        successful_jobs = sum(1 for success in results.values() if success)
        total_jobs = len(results)
        
        logger.info("=" * 60)
        logger.info("Incremental update summary:")
        logger.info(f"  Total jobs: {total_jobs}")
        logger.info(f"  Successful: {successful_jobs}")
        logger.info(f"  Failed: {total_jobs - successful_jobs}")
        logger.info(f"  Total duration: {total_duration:.1f} seconds")
        
        for job_name, success in results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            logger.info(f"  {job_name}: {status}")
        
        logger.info("=" * 60)
        
        return results
    
    def setup_scheduled_jobs(self):
        """设置定时任务"""
        for job_name, job_info in self.jobs.items():
            if job_info['enabled'] and 'default_schedule' in job_info:
                schedule.every().day.at(job_info['default_schedule']).do(
                    self.run_single_job, job_name
                )
                logger.info(f"Scheduled {job_name} at {job_info['default_schedule']}")
    
    def run_scheduler(self):
        """运行调度器"""
        logger.info("Starting incremental update scheduler...")
        self.setup_scheduled_jobs()
        
        logger.info("Scheduler is running. Press Ctrl+C to stop.")
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("Scheduler stopped by user")
    
    def get_job_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        from 增量处理框架 import IncrementalProcessor
        
        processor = IncrementalProcessor()
        status = {}
        
        for job_name in self.jobs.keys():
            watermark = processor.get_last_watermark(f"{job_name}_incremental")
            status[job_name] = {
                'enabled': self.jobs[job_name]['enabled'],
                'description': self.jobs[job_name]['description'],
                'last_update': watermark['last_update_time'] if watermark else None,
                'last_trade_date': watermark['last_trade_date'] if watermark else None,
                'status': watermark['status'] if watermark else 'NEVER_RUN',
                'records_processed': watermark['records_processed'] if watermark else 0
            }
        
        return status

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增量数据更新调度器')
    parser.add_argument('--job', choices=['stock_daily', 'index', 'financial', 'all'], 
                       default='all', help='要运行的任务')
    parser.add_argument('--start-date', help='强制开始日期 (YYYYMMDD)')
    parser.add_argument('--end-date', help='结束日期 (YYYYMMDD)')
    parser.add_argument('--schedule', action='store_true', help='运行定时调度器')
    parser.add_argument('--status', action='store_true', help='查看任务状态')
    
    args = parser.parse_args()
    
    scheduler = IncrementalScheduler()
    
    if args.status:
        # 显示任务状态
        status = scheduler.get_job_status()
        print("\n" + "=" * 80)
        print("增量更新任务状态")
        print("=" * 80)
        
        for job_name, job_status in status.items():
            print(f"\n{job_name.upper()}:")
            print(f"  描述: {job_status['description']}")
            print(f"  状态: {job_status['status']}")
            print(f"  启用: {'是' if job_status['enabled'] else '否'}")
            print(f"  最后更新: {job_status['last_update'] or '从未运行'}")
            print(f"  最后交易日: {job_status['last_trade_date'] or 'N/A'}")
            print(f"  处理记录数: {job_status['records_processed']}")
        
        print("\n" + "=" * 80)
        return
    
    if args.schedule:
        # 运行定时调度器
        scheduler.run_scheduler()
        return
    
    # 运行增量更新
    if args.job == 'all':
        results = scheduler.run_all_jobs(args.start_date, args.end_date)
        # 如果有任务失败，退出码为1
        if not all(results.values()):
            sys.exit(1)
    else:
        success = scheduler.run_single_job(args.job, args.start_date, args.end_date)
        if not success:
            sys.exit(1)

if __name__ == "__main__":
    main()
