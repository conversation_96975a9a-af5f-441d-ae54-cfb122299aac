#!/usr/bin/env python3
"""
增量功能测试脚本
用于验证增量更新功能是否正常工作
"""

import logging
import sys
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text

# 导入增量处理器
from 增量处理框架 import IncrementalProcessor
from 股票日线增量 import StockDailyIncrementalProcessor
from 指数增量 import IndexIncrementalProcessor
from 财务数据增量 import FinancialDataIncrementalProcessor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IncrementalTester:
    """增量功能测试器"""
    
    def __init__(self):
        self.engine = create_engine("mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4")
    
    def test_watermark_table(self):
        """测试水位线表功能"""
        logger.info("Testing watermark table functionality...")
        
        try:
            processor = IncrementalProcessor()
            
            # 测试创建水位线记录
            test_job = 'test_job'
            test_date = '20240315'
            
            processor.update_watermark(test_job, test_date, 100, 'SUCCESS')
            logger.info("✅ Watermark update successful")
            
            # 测试读取水位线
            watermark = processor.get_last_watermark(test_job)
            if watermark and watermark['last_trade_date'] == test_date:
                logger.info("✅ Watermark retrieval successful")
            else:
                logger.error("❌ Watermark retrieval failed")
                return False
            
            # 清理测试数据
            with self.engine.connect() as conn:
                conn.execute(text(f"DELETE FROM incremental_watermarks WHERE job_name = '{test_job}'"))
                conn.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Watermark table test failed: {e}")
            return False
    
    def test_stock_daily_incremental(self):
        """测试股票日线增量功能"""
        logger.info("Testing stock daily incremental functionality...")
        
        try:
            processor = StockDailyIncrementalProcessor()
            
            # 测试处理少量数据
            test_start_date = (datetime.now() - timedelta(days=5)).strftime('%Y%m%d')
            test_end_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
            
            logger.info(f"Testing with date range: {test_start_date} to {test_end_date}")
            
            # 获取股票列表（只测试前3只）
            stock_list = processor.get_stock_list()
            if stock_list is None or stock_list.empty:
                logger.error("❌ Failed to get stock list")
                return False
            
            test_stocks = stock_list.head(3)
            logger.info(f"Testing with {len(test_stocks)} stocks")
            
            # 测试处理单个股票
            for _, stock in test_stocks.iterrows():
                ts_code = stock['ts_code']
                symbol = stock['symbol']
                name = stock['name']
                
                try:
                    records = processor.process_stock_data(ts_code, symbol, name, test_start_date, test_end_date)
                    logger.info(f"✅ Processed {ts_code}: {records} records")
                except Exception as e:
                    logger.warning(f"⚠️  Failed to process {ts_code}: {e}")
                    continue
            
            logger.info("✅ Stock daily incremental test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Stock daily incremental test failed: {e}")
            return False
    
    def test_index_incremental(self):
        """测试指数增量功能"""
        logger.info("Testing index incremental functionality...")
        
        try:
            processor = IndexIncrementalProcessor()
            
            # 测试处理少量数据
            test_start_date = (datetime.now() - timedelta(days=5)).strftime('%Y%m%d')
            test_end_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
            
            logger.info(f"Testing with date range: {test_start_date} to {test_end_date}")
            
            # 测试处理单个指数
            test_index = '000001.SH'  # 上证综指
            
            try:
                records = processor.process_index_data(test_index, test_start_date, test_end_date)
                logger.info(f"✅ Processed {test_index}: {records} records")
            except Exception as e:
                logger.warning(f"⚠️  Failed to process {test_index}: {e}")
            
            logger.info("✅ Index incremental test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Index incremental test failed: {e}")
            return False
    
    def test_financial_incremental(self):
        """测试财务数据增量功能"""
        logger.info("Testing financial incremental functionality...")
        
        try:
            processor = FinancialDataIncrementalProcessor()
            
            # 获取最近的报告期
            periods = ['20231231']  # 测试2023年年报
            
            # 获取股票列表（只测试前2只）
            stock_list = processor.get_stock_list()
            if stock_list is None or stock_list.empty:
                logger.error("❌ Failed to get stock list")
                return False
            
            test_stocks = stock_list.head(2)
            logger.info(f"Testing with {len(test_stocks)} stocks, periods: {periods}")
            
            # 测试处理单个股票的财务数据
            for _, stock in test_stocks.iterrows():
                ts_code = stock['ts_code']
                
                try:
                    records = processor.process_stock_financial_data(ts_code, periods)
                    logger.info(f"✅ Processed financial data for {ts_code}: {records} records")
                except Exception as e:
                    logger.warning(f"⚠️  Failed to process financial data for {ts_code}: {e}")
                    continue
            
            logger.info("✅ Financial incremental test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Financial incremental test failed: {e}")
            return False
    
    def test_database_connectivity(self):
        """测试数据库连接"""
        logger.info("Testing database connectivity...")
        
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                if result.fetchone()[0] == 1:
                    logger.info("✅ Database connection successful")
                    return True
                else:
                    logger.error("❌ Database connection failed")
                    return False
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 60)
        logger.info("Starting incremental functionality tests")
        logger.info("=" * 60)
        
        tests = [
            ("Database Connectivity", self.test_database_connectivity),
            ("Watermark Table", self.test_watermark_table),
            ("Stock Daily Incremental", self.test_stock_daily_incremental),
            ("Index Incremental", self.test_index_incremental),
            ("Financial Incremental", self.test_financial_incremental),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Testing {test_name} ---")
            try:
                success = test_func()
                results[test_name] = success
            except Exception as e:
                logger.error(f"❌ {test_name} test crashed: {e}")
                results[test_name] = False
        
        # 汇总结果
        logger.info("\n" + "=" * 60)
        logger.info("Test Results Summary:")
        logger.info("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, success in results.items():
            status = "✅ PASSED" if success else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
            if success:
                passed += 1
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Incremental functionality is working correctly.")
            return True
        else:
            logger.error("⚠️  Some tests failed. Please check the logs above.")
            return False

def main():
    """主函数"""
    tester = IncrementalTester()
    
    success = tester.run_all_tests()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ 增量功能测试通过！")
        print("你现在可以使用以下命令开始增量更新：")
        print("python 增量更新调度器.py --job all")
        print("=" * 60)
        sys.exit(0)
    else:
        print("\n" + "=" * 60)
        print("❌ 增量功能测试失败！")
        print("请检查上面的错误信息并修复问题。")
        print("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    main()
