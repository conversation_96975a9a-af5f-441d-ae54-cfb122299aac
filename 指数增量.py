import pandas as pd
from sqlalchemy import text, create_engine
import logging
from datetime import datetime, timedelta
from 增量处理框架 import IncrementalProcessor, RateLimiter, api_call_with_retry, pro, logger

class IndexIncrementalProcessor(IncrementalProcessor):
    """指数数据增量处理器"""
    
    def __init__(self, database_url: str = None):
        # 使用指数数据库
        if database_url is None:
            database_url = "mysql+pymysql://root:12345678@localhost:3306/index?charset=utf8mb4"
        
        super().__init__(database_url)
        self.rate_limiter = RateLimiter()
        
        # 指数列表
        self.indices = [
            '000001.SH',  # 上证综指
            '000016.SH',  # 上证50
            '000300.SH',  # 沪深300
            '000852.SH',  # 中证1000
            '000905.SH',  # 中证500
            '932000.CSI', # 中证2000
            '399001.SZ',  # 深证成指
            '399006.SZ'   # 创业板指
        ]
        
        self._ensure_index_database()
    
    def _ensure_index_database(self):
        """确保指数数据库存在"""
        try:
            base_engine = create_engine("mysql+pymysql://root:12345678@localhost:3306/?charset=utf8mb4")
            with base_engine.connect() as conn:
                conn.execute(text("CREATE DATABASE IF NOT EXISTS `index`"))
                conn.commit()
            base_engine.dispose()
            logger.info("Index database ensured")
        except Exception as e:
            logger.error(f"Error creating index database: {e}")
            raise
    
    def process_index_data(self, ts_code: str, start_date: str, end_date: str) -> int:
        """处理单个指数的数据"""
        try:
            logger.info(f"Processing index {ts_code} from {start_date} to {end_date}")
            
            # 获取指数日线数据
            df = api_call_with_retry(
                pro.index_daily,
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                rate_limiter=self.rate_limiter
            )
            
            if df is None or df.empty:
                logger.warning(f"No data for index {ts_code}")
                return 0
            
            # 重命名列以匹配目标格式
            df = df.rename(columns={
                'trade_date': 'candle_end_time',
                'ts_code': 'index_code',
                'vol': 'volume'
            })
            
            # 选择需要的列
            df = df[['candle_end_time', 'open', 'high', 'low', 'close', 'amount', 'volume', 'index_code']]
            
            # 转换日期格式
            df['candle_end_time'] = pd.to_datetime(df['candle_end_time'])
            
            # 创建表名
            table_name = ts_code.lower().replace('.', '')
            
            # 使用UPSERT方式插入数据
            self.upsert_index_data(df, table_name)
            
            logger.info(f"Processed index {ts_code}: {len(df)} records")
            return len(df)
            
        except Exception as e:
            logger.error(f"Error processing index {ts_code}: {e}")
            raise
    
    def upsert_index_data(self, df: pd.DataFrame, table_name: str):
        """使用UPSERT方式插入指数数据"""
        if df.empty:
            return
        
        try:
            # 确保表存在
            self.ensure_index_table_exists(table_name, df)
            
            # 准备UPSERT语句
            columns = df.columns.tolist()
            placeholders = ', '.join([f':{col}' for col in columns])
            update_clause = ', '.join([f'{col} = VALUES({col})' for col in columns if col not in ['index_code', 'candle_end_time']])
            
            upsert_sql = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({placeholders})
                ON DUPLICATE KEY UPDATE {update_clause}
            """
            
            # 批量插入
            with self.engine.connect() as conn:
                for _, row in df.iterrows():
                    row_dict = row.to_dict()
                    # 处理日期格式
                    if 'candle_end_time' in row_dict:
                        row_dict['candle_end_time'] = row_dict['candle_end_time'].strftime('%Y-%m-%d')
                    conn.execute(text(upsert_sql), row_dict)
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error upserting data to {table_name}: {e}")
            raise
    
    def ensure_index_table_exists(self, table_name: str, sample_df: pd.DataFrame):
        """确保指数表存在"""
        try:
            with self.engine.connect() as conn:
                # 检查表是否存在
                result = conn.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = '{table_name}'
                """))
                
                if result.fetchone()[0] == 0:
                    # 表不存在，创建表
                    logger.info(f"Creating index table {table_name}")
                    sample_df.head(0).to_sql(table_name, self.engine, if_exists='replace', index=False)
                    
                    # 添加主键约束
                    conn.execute(text(f"""
                        ALTER TABLE {table_name} 
                        ADD PRIMARY KEY (index_code, candle_end_time)
                    """))
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"Error ensuring index table {table_name} exists: {e}")
            # 如果创建表失败，尝试使用pandas的方式
            try:
                sample_df.head(0).to_sql(table_name, self.engine, if_exists='replace', index=False)
            except:
                pass
    
    def process_incremental_data(self, start_date: str, end_date: str = None) -> int:
        """处理增量数据"""
        logger.info(f"Processing index incremental data from {start_date} to {end_date}")
        
        total_records = 0
        processed_count = 0
        failed_indices = []
        
        for ts_code in self.indices:
            try:
                records = self.process_index_data(ts_code, start_date, end_date)
                total_records += records
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Failed to process index {ts_code}: {e}")
                failed_indices.append({
                    'ts_code': ts_code,
                    'error': str(e)
                })
                continue
        
        # 重试失败的指数
        if failed_indices:
            logger.info(f"Retrying {len(failed_indices)} failed indices...")
            retry_success = 0
            
            for index_info in failed_indices:
                try:
                    records = self.process_index_data(
                        index_info['ts_code'], 
                        start_date, 
                        end_date
                    )
                    total_records += records
                    retry_success += 1
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Retry failed for index {index_info['ts_code']}: {e}")
                    continue
            
            logger.info(f"Retry completed: {retry_success}/{len(failed_indices)} succeeded")
        
        logger.info(f"Index incremental processing completed: {processed_count} indices, {total_records} total records")
        return total_records

def run_index_incremental(force_start_date: str = None, end_date: str = None):
    """运行指数增量更新"""
    processor = IndexIncrementalProcessor()
    return processor.run_incremental_update(
        job_name='index_incremental',
        force_start_date=force_start_date,
        end_date=end_date,
        safe_lag_days=1
    )

if __name__ == "__main__":
    # 运行增量更新
    try:
        records = run_index_incremental()
        logger.info(f"Index incremental update completed: {records} records processed")
    except Exception as e:
        logger.error(f"Index incremental update failed: {e}")
