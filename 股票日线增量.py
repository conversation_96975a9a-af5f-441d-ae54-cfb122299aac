import pandas as pd
from sqlalchemy import text
import logging
from datetime import datetime, timedelta
from 增量处理框架 import IncrementalProcessor, RateLimiter, api_call_with_retry, pro, logger

class StockDailyIncrementalProcessor(IncrementalProcessor):
    """股票日线数据增量处理器"""
    
    def __init__(self, database_url: str = None):
        super().__init__(database_url)
        self.rate_limiter = RateLimiter()
        self.batch_size = 10
        self.reweight_factor = 1.0
    
    def get_stock_list(self):
        """获取股票列表"""
        try:
            stock_list = api_call_with_retry(
                pro.stock_basic,
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name',
                rate_limiter=self.rate_limiter
            )
            return stock_list
        except Exception as e:
            logger.error(f"Failed to get stock list: {e}")
            raise
    
    def process_stock_data(self, ts_code: str, symbol: str, name: str, 
                          start_date: str, end_date: str) -> int:
        """处理单个股票的数据"""
        try:
            # 获取日线数据
            df_daily = api_call_with_retry(
                pro.daily,
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                rate_limiter=self.rate_limiter
            )
            
            if df_daily is None or df_daily.empty:
                logger.warning(f"No daily data for {ts_code} ({name})")
                return 0
            
            # 获取基本指标数据
            df_basic = api_call_with_retry(
                pro.daily_basic,
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,trade_date,close,turnover_rate,turnover_rate_f,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv',
                rate_limiter=self.rate_limiter
            )
            
            if df_basic is None:
                df_basic = pd.DataFrame()
            
            # 合并数据
            if not df_basic.empty:
                df = pd.merge(df_daily, df_basic, on=['ts_code', 'trade_date'], 
                             how='left', suffixes=('', '_basic'))
            else:
                df = df_daily.copy()
            
            # 添加复权因子
            df['reweight_factor'] = self.reweight_factor
            df['reweighted_close'] = df['close'] * df['reweight_factor']
            df['reweighted_open'] = df['open'] * df['reweight_factor']
            df['reweighted_high'] = df['high'] * df['reweight_factor']
            df['reweighted_low'] = df['low'] * df['reweight_factor']
            
            # 表名
            table_name = f'stock_daily_{symbol}'
            
            # 使用UPSERT方式插入数据
            self.upsert_stock_data(df, table_name)
            
            logger.info(f"Processed {ts_code} ({name}): {len(df)} records")
            return len(df)
            
        except Exception as e:
            logger.error(f"Error processing {ts_code} ({name}): {e}")
            raise
    
    def upsert_stock_data(self, df: pd.DataFrame, table_name: str):
        """使用UPSERT方式插入股票数据"""
        if df.empty:
            return
        
        try:
            # 首先确保表存在
            self.ensure_table_exists(table_name, df)
            
            # 准备UPSERT语句
            columns = df.columns.tolist()
            placeholders = ', '.join([f':{col}' for col in columns])
            update_clause = ', '.join([f'{col} = VALUES({col})' for col in columns if col not in ['ts_code', 'trade_date']])
            
            upsert_sql = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({placeholders})
                ON DUPLICATE KEY UPDATE {update_clause}
            """
            
            # 批量插入
            with self.engine.connect() as conn:
                for _, row in df.iterrows():
                    conn.execute(text(upsert_sql), row.to_dict())
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error upserting data to {table_name}: {e}")
            raise
    
    def ensure_table_exists(self, table_name: str, sample_df: pd.DataFrame):
        """确保表存在，如果不存在则创建"""
        try:
            with self.engine.connect() as conn:
                # 检查表是否存在
                result = conn.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = '{table_name}'
                """))
                
                if result.fetchone()[0] == 0:
                    # 表不存在，创建表
                    logger.info(f"Creating table {table_name}")
                    sample_df.head(0).to_sql(table_name, self.engine, if_exists='replace', index=False)
                    
                    # 添加主键约束
                    conn.execute(text(f"""
                        ALTER TABLE {table_name} 
                        ADD PRIMARY KEY (ts_code, trade_date)
                    """))
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"Error ensuring table {table_name} exists: {e}")
            # 如果创建表失败，尝试使用pandas的方式
            try:
                sample_df.head(0).to_sql(table_name, self.engine, if_exists='replace', index=False)
            except:
                pass
    
    def process_incremental_data(self, start_date: str, end_date: str = None) -> int:
        """处理增量数据"""
        logger.info(f"Processing stock daily incremental data from {start_date} to {end_date}")
        
        # 获取股票列表
        stock_list = self.get_stock_list()
        if stock_list is None or stock_list.empty:
            logger.error("Failed to get stock list")
            return 0
        
        total_stocks = len(stock_list)
        total_records = 0
        processed_count = 0
        failed_stocks = []
        
        logger.info(f"Total stocks to process: {total_stocks}")
        
        # 分批处理股票
        for batch_start in range(0, total_stocks, self.batch_size):
            batch_end = min(batch_start + self.batch_size, total_stocks)
            batch_stocks = stock_list.iloc[batch_start:batch_end]
            
            logger.info(f"Processing batch {batch_start//self.batch_size + 1}/{(total_stocks-1)//self.batch_size + 1} "
                       f"(stocks {batch_start + 1}-{batch_end})")
            
            for _, stock in batch_stocks.iterrows():
                ts_code = stock['ts_code']
                symbol = stock['symbol']
                name = stock['name']
                
                try:
                    records = self.process_stock_data(ts_code, symbol, name, start_date, end_date)
                    total_records += records
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to process {ts_code} ({name}): {e}")
                    failed_stocks.append({
                        'ts_code': ts_code,
                        'symbol': symbol,
                        'name': name,
                        'error': str(e)
                    })
                    continue
        
        # 重试失败的股票
        if failed_stocks:
            logger.info(f"Retrying {len(failed_stocks)} failed stocks...")
            retry_success = 0
            
            for stock_info in failed_stocks:
                try:
                    records = self.process_stock_data(
                        stock_info['ts_code'], 
                        stock_info['symbol'], 
                        stock_info['name'], 
                        start_date, 
                        end_date
                    )
                    total_records += records
                    retry_success += 1
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Retry failed for {stock_info['ts_code']}: {e}")
                    continue
            
            logger.info(f"Retry completed: {retry_success}/{len(failed_stocks)} succeeded")
        
        logger.info(f"Incremental processing completed: {processed_count} stocks, {total_records} total records")
        return total_records

def run_stock_daily_incremental(force_start_date: str = None, end_date: str = None):
    """运行股票日线增量更新"""
    processor = StockDailyIncrementalProcessor()
    return processor.run_incremental_update(
        job_name='stock_daily_incremental',
        force_start_date=force_start_date,
        end_date=end_date,
        safe_lag_days=1
    )

if __name__ == "__main__":
    # 运行增量更新
    try:
        records = run_stock_daily_incremental()
        logger.info(f"Stock daily incremental update completed: {records} records processed")
    except Exception as e:
        logger.error(f"Stock daily incremental update failed: {e}")
