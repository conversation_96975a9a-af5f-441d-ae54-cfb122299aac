#!/usr/bin/env python3
"""
基础测试 - 不导入tushare
"""

import sys

def test_basic_imports():
    """测试基础导入"""
    try:
        print("测试基础包导入...")
        import pandas as pd
        print("✅ pandas 导入成功")
        
        from sqlalchemy import create_engine, text
        print("✅ sqlalchemy 导入成功")
        
        from datetime import datetime, timedelta
        print("✅ datetime 导入成功")
        
        import logging
        print("✅ logging 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 基础包导入失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        print("测试数据库连接...")
        from sqlalchemy import create_engine, text
        
        DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone()[0] == 1:
                print("✅ 数据库连接成功")
                return True
            else:
                print("❌ 数据库连接失败")
                return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_tushare():
    """测试tushare导入和初始化"""
    try:
        print("测试 tushare 导入...")
        import tushare as ts
        print("✅ tushare 导入成功")
        
        print("测试 tushare 初始化...")
        TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
        ts.set_token(TUSHARE_TOKEN)
        print("✅ tushare token 设置成功")
        
        pro = ts.pro_api()
        print("✅ tushare pro_api 初始化成功")
        
        return True
    except Exception as e:
        print(f"❌ tushare 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("基础环境测试")
    print("=" * 50)
    
    tests = [
        ("基础包导入", test_basic_imports),
        ("数据库连接", test_database_connection),
        ("Tushare", test_tushare)
    ]
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        success = test_func()
        if not success:
            print(f"❌ {test_name} 测试失败")
            return False
    
    print("\n" + "=" * 50)
    print("✅ 所有基础测试通过！")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
