import pandas as pd
from sqlalchemy import text
import logging
import re
from datetime import datetime, timedelta
from 增量处理框架 import IncrementalProcessor, RateLimiter, api_call_with_retry, pro, logger

class FinancialDataIncrementalProcessor(IncrementalProcessor):
    """财务数据增量处理器"""
    
    def __init__(self, database_url: str = None):
        from 增量处理框架 import DATABASE_URL as DEFAULT_DATABASE_URL
        if database_url is None:
            database_url = DEFAULT_DATABASE_URL
        super().__init__(database_url)
        self.rate_limiter = RateLimiter()
        self.batch_size = 5  # 财务数据API调用较慢，减少批次大小
    
    def clean_table_name(self, ts_code: str) -> str:
        """转换股票代码为有效的表名"""
        return re.sub(r'[^\w]', '_', ts_code.lower())
    
    def get_stock_list(self):
        """获取股票列表"""
        try:
            stock_list = api_call_with_retry(
                pro.stock_basic,
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name,area,industry,list_date',
                rate_limiter=self.rate_limiter
            )
            return stock_list
        except Exception as e:
            logger.error(f"Failed to get stock list: {e}")
            raise
    
    def get_recent_report_periods(self, start_date: str) -> list:
        """获取需要更新的报告期"""
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        current_dt = datetime.now()
        
        periods = []
        year = start_dt.year
        
        while year <= current_dt.year:
            # 每年的四个报告期
            for period in ['0331', '0630', '0930', '1231']:
                period_date = f"{year}{period}"
                if period_date >= start_date:
                    periods.append(period_date)
            year += 1
        
        return periods
    
    def process_balance_sheet(self, ts_code: str, table_suffix: str, periods: list) -> int:
        """处理资产负债表数据"""
        total_records = 0
        
        for period in periods:
            try:
                df = api_call_with_retry(
                    pro.balancesheet,
                    ts_code=ts_code,
                    period=period,
                    fields='ts_code,ann_date,f_ann_date,end_date,report_type,comp_type,total_share,cap_rese,undistr_profit,surplus_rese,special_rese,money_cap,trad_asset,notes_receiv,accounts_receiv,oth_receiv,prepayment,div_receiv,int_receiv,inventories,amor_exp,nca_within_1y,sett_rsrv,loanto_oth_bank_fi,premium_receiv,reinsur_receiv,reinsur_res_receiv,pur_resale_fa,oth_cur_assets,total_cur_assets,fa_avail_for_sale,htm_invest,lt_eqt_invest,invest_real_estate,time_deposits,oth_assets,lt_rec,fix_assets,cip,const_materials,fixed_assets_disp,produc_bio_assets,oil_and_gas_assets,intan_assets,r_and_d,goodwill,lt_amor_exp,defer_tax_assets,decr_in_disbur,oth_nca,total_nca,cash_reser_cb,depos_in_oth_bfi,prec_metals,deriv_assets,rr_reins_une_prem,rr_reins_outstd_cla,rr_reins_lins_liab,rr_reins_lthins_liab,refund_depos,ph_pledge_loans,refund_cap_depos,indep_acct_assets,client_depos,client_prov,transac_seat_fee,invest_as_receiv,total_assets,st_borr,borr_frm_cb,depos_frm_oth_bfi,agent_trad_secu,agent_underw_secu,payroll_payable,taxes_payable,int_payable,div_payable,oth_payable,acc_exp,deferred_inc,st_bonds_payable,payable_to_reinsurer,rsrv_insur_cont,acting_trading_sec,acting_uw_sec,non_cur_liab_due_1y,oth_cur_liab,total_cur_liab,bond_payable,lt_payable,specific_payables,estimated_liab,defer_tax_liab,defer_inc_non_cur_liab,oth_ncl,total_ncl,depos_oth_bfi,deriv_liab,depos,agency_bus_liab,oth_liab,prem_receiv_adva,depos_received,ph_invest,reser_une_prem,reser_outstd_claims,reser_lins_liab,reser_lthins_liab,indept_acc_liab,pledge_borr,indem_payable,policy_div_payable,total_liab,treasury_share,ordin_risk_reser,forex_differ,invest_loss_unconf,minority_int,total_hldr_eqy_exc_min_int,total_hldr_eqy_inc_min_int,total_liab_hldr_eqy,lt_payroll_payable,oth_comp_income,oth_eqt_tools,oth_eqt_tools_p_shr,lending_funds,acc_receivable,st_fin_payable,payables,hfs_assets,hfs_sales',
                    rate_limiter=self.rate_limiter
                )
                
                if df is not None and not df.empty:
                    table_name = f'balance_sheet_{table_suffix}'
                    self.upsert_financial_data(df, table_name)
                    total_records += len(df)
                    
            except Exception as e:
                logger.warning(f"Error getting balance sheet for {ts_code} period {period}: {e}")
                continue
        
        return total_records
    
    def process_income_statement(self, ts_code: str, table_suffix: str, periods: list) -> int:
        """处理利润表数据"""
        total_records = 0
        
        for period in periods:
            try:
                df = api_call_with_retry(
                    pro.income,
                    ts_code=ts_code,
                    period=period,
                    fields='ts_code,ann_date,f_ann_date,end_date,report_type,comp_type,basic_eps,diluted_eps,total_revenue,revenue,int_income,prem_earned,comm_income,n_commis_income,n_oth_income,n_oth_b_income,prem_income,out_prem,une_prem_reser,reins_income,n_sec_tb_income,n_sec_uw_income,n_asset_mg_income,oth_b_income,fv_value_chg_gain,invest_income,ass_invest_income,forex_gain,total_cogs,oper_cost,int_exp,comm_exp,biz_tax_surchg,sell_exp,admin_exp,fin_exp,assets_impair_loss,prem_refund,compens_payout,reser_insur_liab,div_payt,reins_exp,oper_exp,compens_payout_refu,insur_reser_refu,reins_cost_refund,other_bus_cost,operate_profit,non_oper_income,non_oper_exp,nca_disploss,total_profit,income_tax,n_income,n_income_attr_p,minority_gain,oth_compr_income,t_compr_income,compr_inc_attr_p,compr_inc_attr_m_s,ebit,ebitda,insurance_exp,undist_profit,distable_profit,rd_exp,fin_exp_int_exp,fin_exp_int_inc,transfer_surplus_rese,transfer_housing_imprest,transfer_oth,adj_lossgain,withdra_legal_surplus,withdra_legal_publi,withdra_biz_devfund,withdra_rese_fund,withdra_oth_ersu,workers_welfare,distr_profit_shrhder,prfshare_payable_dvd,comshare_payable_dvd,capit_comstock_div,continued_net_profit,end_net_profit',
                    rate_limiter=self.rate_limiter
                )
                
                if df is not None and not df.empty:
                    table_name = f'income_statement_{table_suffix}'
                    self.upsert_financial_data(df, table_name)
                    total_records += len(df)
                    
            except Exception as e:
                logger.warning(f"Error getting income statement for {ts_code} period {period}: {e}")
                continue
        
        return total_records
    
    def process_cash_flow(self, ts_code: str, table_suffix: str, periods: list) -> int:
        """处理现金流量表数据"""
        total_records = 0
        
        for period in periods:
            try:
                df = api_call_with_retry(
                    pro.cashflow,
                    ts_code=ts_code,
                    period=period,
                    fields='ts_code,ann_date,f_ann_date,end_date,report_type,comp_type,net_profit,finan_exp,c_fr_sale_sg,recp_tax_rends,n_depos_incr_fi,n_incr_loans_cb,n_inc_borr_oth_fi,prem_fr_orig_contr,n_incr_insured_dep,n_reinsur_prem,n_incr_disp_tfa,ifc_cash_incr,n_incr_disp_faas,n_incr_loans_oth_bank,n_cap_incr_repur,c_fr_oth_operate_a,c_inf_fr_operate_a,c_paid_goods_s,c_paid_to_for_empl,c_paid_for_taxes,n_incr_clt_loan_adv,n_incr_dep_cbob,c_pay_claims_orig_inco,pay_handling_chrg,pay_comm_insur_plcy,oth_cash_pay_oper_act,st_cash_out_act,n_cashflow_act,oth_recp_ral_inv_act,c_disp_withdrwl_invest,c_recp_return_invest,n_recp_disp_fiolta,n_recp_disp_sobu,stot_inflows_inv_act,c_pay_acq_const_fiolta,c_paid_invest,n_disp_subs_oth_biz,oth_pay_ral_inv_act,n_incr_pledge_loan,stot_out_inv_act,n_cashflow_inv_act,c_recp_borrow,proc_issue_bonds,oth_cash_recp_ral_fnc_act,stot_cash_in_fnc_act,free_cashflow,c_prepay_amt_borr,c_pay_dist_dpcp_int_exp,incl_dvd_profit_paid_sc_ms,oth_cashpay_ral_fnc_act,stot_cashout_fnc_act,n_cash_flows_fnc_act,eff_fx_flu_cash,n_incr_cash_cash_equ,c_cash_equ_beg_period,c_cash_equ_end_period,c_recp_cap_contrib,incl_cash_rec_saims,uncon_invest_loss,prov_depr_assets,depr_fa_coga_dpba,amort_intang_assets,lt_amort_deferred_exp,decr_deferred_exp,incr_acc_exp,loss_disp_fiolta,loss_scr_fa,loss_fv_chg,invest_loss,decr_def_inc_tax_assets,incr_def_inc_tax_liab,decr_inventories,decr_oper_payable,incr_oper_payable,others,im_net_cashflow_oper_act,conv_debt_into_cap,conv_copbonds_due_within_1y,fa_fnc_leases,end_bal_cash,beg_bal_cash,end_bal_cash_equ,beg_bal_cash_equ,im_n_incr_cash_equ',
                    rate_limiter=self.rate_limiter
                )
                
                if df is not None and not df.empty:
                    table_name = f'cash_flow_{table_suffix}'
                    self.upsert_financial_data(df, table_name)
                    total_records += len(df)
                    
            except Exception as e:
                logger.warning(f"Error getting cash flow for {ts_code} period {period}: {e}")
                continue
        
        return total_records
    
    def upsert_financial_data(self, df: pd.DataFrame, table_name: str):
        """使用UPSERT方式插入财务数据"""
        if df.empty:
            return
        
        try:
            # 确保表存在
            self.ensure_financial_table_exists(table_name, df)
            
            # 准备UPSERT语句
            columns = df.columns.tolist()
            placeholders = ', '.join([f':{col}' for col in columns])
            update_clause = ', '.join([f'{col} = VALUES({col})' for col in columns if col not in ['ts_code', 'end_date']])
            
            upsert_sql = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({placeholders})
                ON DUPLICATE KEY UPDATE {update_clause}
            """
            
            # 批量插入
            with self.engine.connect() as conn:
                for _, row in df.iterrows():
                    conn.execute(text(upsert_sql), row.to_dict())
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error upserting data to {table_name}: {e}")
            # 如果UPSERT失败，尝试替换整个表
            try:
                df.to_sql(table_name, self.engine, if_exists='replace', index=False)
            except Exception as e2:
                logger.error(f"Error replacing table {table_name}: {e2}")
                raise
    
    def ensure_financial_table_exists(self, table_name: str, sample_df: pd.DataFrame):
        """确保财务表存在"""
        try:
            with self.engine.connect() as conn:
                # 检查表是否存在
                result = conn.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = '{table_name}'
                """))
                
                if result.fetchone()[0] == 0:
                    # 表不存在，创建表
                    logger.info(f"Creating financial table {table_name}")
                    sample_df.head(0).to_sql(table_name, self.engine, if_exists='replace', index=False)
                    
                    # 添加主键约束
                    try:
                        conn.execute(text(f"""
                            ALTER TABLE {table_name} 
                            ADD PRIMARY KEY (ts_code, end_date)
                        """))
                        conn.commit()
                    except:
                        # 如果添加主键失败，忽略错误
                        pass
                    
        except Exception as e:
            logger.error(f"Error ensuring financial table {table_name} exists: {e}")
            # 如果创建表失败，尝试使用pandas的方式
            try:
                sample_df.head(0).to_sql(table_name, self.engine, if_exists='replace', index=False)
            except:
                pass
    
    def process_stock_financial_data(self, ts_code: str, periods: list) -> int:
        """处理单个股票的财务数据"""
        table_suffix = self.clean_table_name(ts_code)
        total_records = 0
        
        try:
            # 处理资产负债表
            balance_records = self.process_balance_sheet(ts_code, table_suffix, periods)
            total_records += balance_records
            
            # 处理利润表
            income_records = self.process_income_statement(ts_code, table_suffix, periods)
            total_records += income_records
            
            # 处理现金流量表
            cashflow_records = self.process_cash_flow(ts_code, table_suffix, periods)
            total_records += cashflow_records
            
            logger.info(f"Processed financial data for {ts_code}: {total_records} total records")
            return total_records
            
        except Exception as e:
            logger.error(f"Error processing financial data for {ts_code}: {e}")
            raise
    
    def process_incremental_data(self, start_date: str, end_date: str = None) -> int:
        """处理增量数据"""
        logger.info(f"Processing financial incremental data from {start_date}")
        
        # 获取需要更新的报告期
        periods = self.get_recent_report_periods(start_date)
        logger.info(f"Report periods to process: {periods}")
        
        # 获取股票列表
        stock_list = self.get_stock_list()
        if stock_list is None or stock_list.empty:
            logger.error("Failed to get stock list")
            return 0
        
        total_stocks = len(stock_list)
        total_records = 0
        processed_count = 0
        failed_stocks = []
        
        logger.info(f"Total stocks to process: {total_stocks}")
        
        # 分批处理股票
        for batch_start in range(0, total_stocks, self.batch_size):
            batch_end = min(batch_start + self.batch_size, total_stocks)
            batch_stocks = stock_list.iloc[batch_start:batch_end]
            
            logger.info(f"Processing batch {batch_start//self.batch_size + 1}/{(total_stocks-1)//self.batch_size + 1} "
                       f"(stocks {batch_start + 1}-{batch_end})")
            
            for _, stock in batch_stocks.iterrows():
                ts_code = stock['ts_code']
                
                try:
                    records = self.process_stock_financial_data(ts_code, periods)
                    total_records += records
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to process financial data for {ts_code}: {e}")
                    failed_stocks.append({
                        'ts_code': ts_code,
                        'error': str(e)
                    })
                    continue
        
        logger.info(f"Financial incremental processing completed: {processed_count} stocks, {total_records} total records")
        return total_records

def run_financial_incremental(force_start_date: str = None, end_date: str = None):
    """运行财务数据增量更新"""
    processor = FinancialDataIncrementalProcessor()
    return processor.run_incremental_update(
        job_name='financial_incremental',
        force_start_date=force_start_date,
        end_date=end_date,
        safe_lag_days=7  # 财务数据延迟较大，使用7天安全延迟
    )

if __name__ == "__main__":
    # 运行增量更新
    try:
        records = run_financial_incremental()
        logger.info(f"Financial incremental update completed: {records} records processed")
    except Exception as e:
        logger.error(f"Financial incremental update failed: {e}")
