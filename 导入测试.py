#!/usr/bin/env python3
"""
简单的导入测试
"""

import sys
import traceback

def test_import(module_name):
    """测试导入模块"""
    try:
        print(f"正在导入 {module_name}...")
        if module_name == "增量处理框架":
            from 增量处理框架 import IncrementalProcessor
        elif module_name == "股票日线增量":
            from 股票日线增量 import StockDailyIncrementalProcessor
        elif module_name == "指数增量":
            from 指数增量 import IndexIncrementalProcessor
        elif module_name == "财务数据增量":
            from 财务数据增量 import FinancialDataIncrementalProcessor
        print(f"✅ {module_name} 导入成功")
        return True
    except Exception as e:
        print(f"❌ {module_name} 导入失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始导入测试...")
    
    modules = [
        "增量处理框架",
        "股票日线增量", 
        "指数增量",
        "财务数据增量"
    ]
    
    for module in modules:
        success = test_import(module)
        if not success:
            print(f"导入 {module} 失败，停止测试")
            return False
    
    print("✅ 所有模块导入成功！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
