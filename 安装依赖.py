#!/usr/bin/env python3
"""
安装增量更新系统所需的依赖包
"""

import subprocess
import sys
import importlib

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("检查并安装增量更新系统依赖包...")
    print("=" * 50)
    
    # 需要的包列表
    required_packages = [
        ("tushare", "tushare"),
        ("pandas", "pandas"),
        ("sqlalchemy", "sqlalchemy"),
        ("pymysql", "pymysql"),
        ("schedule", "schedule")
    ]
    
    missing_packages = []
    
    # 检查已安装的包
    for import_name, package_name in required_packages:
        if check_package(import_name):
            print(f"✅ {package_name} 已安装")
        else:
            print(f"❌ {package_name} 未安装")
            missing_packages.append(package_name)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n需要安装 {len(missing_packages)} 个包...")
        
        for package in missing_packages:
            print(f"正在安装 {package}...")
            if install_package(package):
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败")
                return False
    else:
        print("\n✅ 所有依赖包都已安装")
    
    print("\n" + "=" * 50)
    print("依赖检查完成！")
    
    # 验证安装
    print("\n验证安装...")
    all_ok = True
    for import_name, package_name in required_packages:
        if check_package(import_name):
            print(f"✅ {package_name}")
        else:
            print(f"❌ {package_name}")
            all_ok = False
    
    if all_ok:
        print("\n🎉 所有依赖包安装成功！")
        print("\n你现在可以运行以下命令测试增量功能：")
        print("python 测试增量功能.py")
        return True
    else:
        print("\n❌ 部分依赖包安装失败，请手动安装：")
        print("pip install tushare pandas sqlalchemy pymysql schedule")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
