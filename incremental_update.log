2025-08-09 21:38:11,693 - INFO - ============================================================
2025-08-09 21:38:11,694 - INFO - Starting incremental functionality tests
2025-08-09 21:38:11,694 - INFO - ============================================================
2025-08-09 21:38:11,695 - INFO - 
--- Testing Database Connectivity ---
2025-08-09 21:38:11,696 - INFO - Testing database connectivity...
2025-08-09 21:38:12,181 - INFO - 
--- Testing Watermark Table ---
2025-08-09 21:38:12,186 - INFO - Testing watermark table functionality...
2025-08-09 21:38:12,257 - INFO - 
--- Testing Stock Daily Incremental ---
2025-08-09 21:38:12,275 - INFO - Testing stock daily incremental functionality...
2025-08-09 21:38:12,368 - INFO - 
--- Testing Index Incremental ---
2025-08-09 21:38:12,371 - INFO - Testing index incremental functionality...
2025-08-09 21:38:12,400 - ERROR - Error creating watermark table: (pymysql.err.OperationalError) (1067, "Invalid default value for 'created_at'")
[SQL: 
                    CREATE TABLE IF NOT EXISTS incremental_watermarks (
                        job_name VARCHAR(100) PRIMARY KEY,
                        last_update_time DATETIME,
                        last_trade_date VARCHAR(8),
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        status VARCHAR(20) DEFAULT 'SUCCESS',
                        records_processed INT DEFAULT 0,
                        error_message TEXT
                    )
                ]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 21:38:12,468 - INFO - 
--- Testing Financial Incremental ---
2025-08-09 21:38:12,473 - INFO - Testing financial incremental functionality...
2025-08-09 21:38:12,519 - INFO - 
============================================================
2025-08-09 21:38:12,520 - INFO - Test Results Summary:
2025-08-09 21:38:12,523 - INFO - ============================================================
2025-08-09 21:38:12,592 - INFO - 
Overall: 1/5 tests passed
