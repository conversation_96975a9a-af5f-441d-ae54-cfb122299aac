#!/usr/bin/env python3
"""
增量更新系统快速开始脚本
"""

import os
import sys
import subprocess
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 增量数据更新系统 - 快速开始")
    print("=" * 60)

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        "增量处理框架.py",
        "股票日线增量.py", 
        "指数增量.py",
        "财务数据增量.py",
        "增量更新调度器.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n📋 {description}")
    print(f"命令: {command}")
    print("-" * 40)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 执行成功")
            if result.stdout:
                print("输出:")
                print(result.stdout)
            return True
        else:
            print("❌ 执行失败")
            if result.stderr:
                print("错误:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 步骤1: 检查文件
    print("\n📁 步骤1: 检查必要文件")
    if not check_files():
        print("\n请确保所有增量更新文件都在当前目录中")
        return False
    
    # 步骤2: 安装依赖
    print("\n📦 步骤2: 检查并安装依赖")
    if not run_command("python 安装依赖.py", "安装依赖包"):
        print("\n请手动安装依赖: pip install tushare pandas sqlalchemy pymysql schedule")
        return False
    
    # 步骤3: 测试功能
    print("\n🧪 步骤3: 测试增量功能")
    if not run_command("python 测试增量功能.py", "测试增量更新功能"):
        print("\n测试失败，请检查配置和网络连接")
        return False
    
    # 步骤4: 显示使用说明
    print("\n" + "=" * 60)
    print("🎉 快速开始完成！")
    print("=" * 60)
    
    print("\n📚 接下来你可以:")
    print("\n1. 查看任务状态:")
    print("   python 增量更新调度器.py --status")
    
    print("\n2. 运行单次增量更新:")
    print("   python 增量更新调度器.py --job all")
    
    print("\n3. 运行特定任务:")
    print("   python 增量更新调度器.py --job stock_daily")
    print("   python 增量更新调度器.py --job index") 
    print("   python 增量更新调度器.py --job financial")
    
    print("\n4. 指定日期范围:")
    print("   python 增量更新调度器.py --job all --start-date 20240101")
    
    print("\n5. 启动定时调度器:")
    print("   python 增量更新调度器.py --schedule")
    
    print("\n6. 查看详细说明:")
    print("   查看 '增量更新使用说明.md' 文件")
    
    print("\n" + "=" * 60)
    print("💡 提示:")
    print("- 首次运行会从最近30天开始增量更新")
    print("- 建议先运行一次全量更新，再使用增量功能")
    print("- 增量更新支持断点续传，可以安全重复运行")
    print("- 查看日志文件了解详细执行情况")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 快速开始完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"\n❌ 快速开始失败 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    sys.exit(0 if success else 1)
