# 增量数据更新系统使用说明

## 概述

本系统为你的全量数据处理脚本添加了增量更新功能，支持：
- 股票日线数据增量更新
- 指数数据增量更新  
- 财务数据增量更新
- 统一的调度管理

## 核心特性

### 1. 水位线管理
- 自动记录每次处理的最后日期
- 下次运行时从上次结束位置继续
- 支持失败重试和状态跟踪

### 2. 数据一致性
- 使用 UPSERT 操作避免重复数据
- 支持幂等操作，重复运行不会产生副作用
- 自动处理主键冲突

### 3. API 频率控制
- 智能的 API 调用频率限制
- 自动重试机制
- 指数退避策略

### 4. 安全延迟
- 默认延迟1天处理，避免数据不完整
- 可配置的安全延迟时间
- 支持回填历史数据

## 文件结构

```
增量处理框架.py          # 基础框架和抽象类
股票日线增量.py          # 股票日线数据增量处理
指数增量.py              # 指数数据增量处理
财务数据增量.py          # 财务数据增量处理
增量更新调度器.py        # 统一调度管理器
增量更新使用说明.md      # 本说明文档
```

## 快速开始

### 1. 安装依赖

确保已安装必要的 Python 包：
```bash
pip install tushare pandas sqlalchemy pymysql schedule
```

### 2. 首次运行

#### 运行所有增量更新：
```bash
python 增量更新调度器.py --job all
```

#### 运行特定任务：
```bash
# 股票日线数据
python 增量更新调度器.py --job stock_daily

# 指数数据  
python 增量更新调度器.py --job index

# 财务数据
python 增量更新调度器.py --job financial
```

### 3. 指定日期范围

```bash
# 从指定日期开始更新
python 增量更新调度器.py --job all --start-date 20240101

# 指定日期范围
python 增量更新调度器.py --job stock_daily --start-date 20240101 --end-date 20240131
```

### 4. 查看任务状态

```bash
python 增量更新调度器.py --status
```

### 5. 定时调度

```bash
# 启动定时调度器（每天自动运行）
python 增量更新调度器.py --schedule
```

## 详细功能说明

### 股票日线增量更新

**功能**：更新所有股票的日线数据和基本指标
**表结构**：每个股票一张表 `stock_daily_{symbol}`
**主键**：`(ts_code, trade_date)`
**更新频率**：建议每日运行
**安全延迟**：1天

**特点**：
- 自动获取股票列表
- 合并日线数据和基本指标
- 添加复权因子计算
- 支持批量处理和失败重试

### 指数增量更新

**功能**：更新主要指数的日线数据
**数据库**：独立的 `index` 数据库
**表结构**：每个指数一张表，如 `000001sh`
**主键**：`(index_code, candle_end_time)`
**更新频率**：建议每日运行
**安全延迟**：1天

**支持的指数**：
- 000001.SH (上证综指)
- 000016.SH (上证50)
- 000300.SH (沪深300)
- 000852.SH (中证1000)
- 000905.SH (中证500)
- 932000.CSI (中证2000)
- 399001.SZ (深证成指)
- 399006.SZ (创业板指)

### 财务数据增量更新

**功能**：更新财务三表数据（资产负债表、利润表、现金流量表）
**表结构**：每个股票三张表
- `balance_sheet_{ts_code}`
- `income_statement_{ts_code}`  
- `cash_flow_{ts_code}`
**主键**：`(ts_code, end_date)`
**更新频率**：建议每周运行
**安全延迟**：7天

**特点**：
- 按报告期更新（季报、年报）
- 自动识别需要更新的报告期
- 处理速度较慢，建议非交易时间运行

## 水位线表结构

系统会自动创建 `incremental_watermarks` 表来跟踪处理进度：

```sql
CREATE TABLE incremental_watermarks (
    job_name VARCHAR(100) PRIMARY KEY,           -- 任务名称
    last_update_time DATETIME,                   -- 最后更新时间
    last_trade_date VARCHAR(8),                  -- 最后处理的交易日
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'SUCCESS',        -- 状态：SUCCESS/FAILED
    records_processed INT DEFAULT 0,             -- 处理的记录数
    error_message TEXT                           -- 错误信息
);
```

## 定时调度配置

默认调度时间：
- 股票日线：每天 09:00
- 指数数据：每天 09:30  
- 财务数据：每天 10:00

可以修改 `增量更新调度器.py` 中的 `default_schedule` 来调整时间。

## 错误处理和监控

### 日志文件
- `incremental_update.log` - 增量处理日志
- `incremental_scheduler.log` - 调度器日志

### 错误处理策略
1. **API 限频**：自动等待和重试
2. **网络错误**：指数退避重试
3. **数据错误**：跳过错误数据，继续处理
4. **失败记录**：记录到水位线表，便于排查

### 监控建议
1. 定期检查日志文件
2. 监控水位线表的状态字段
3. 设置数据量异常告警
4. 定期验证数据完整性

## 最佳实践

### 1. 首次部署
```bash
# 1. 先运行一次全量更新（使用你现有的脚本）
python 全量股票日线.py

# 2. 初始化增量更新（从最近30天开始）
python 增量更新调度器.py --job all

# 3. 启动定时调度
python 增量更新调度器.py --schedule
```

### 2. 日常维护
```bash
# 查看状态
python 增量更新调度器.py --status

# 手动补数据
python 增量更新调度器.py --job stock_daily --start-date 20240101 --end-date 20240105
```

### 3. 故障恢复
```bash
# 如果某天的数据处理失败，可以重新处理
python 增量更新调度器.py --job all --start-date 20240315 --end-date 20240315
```

## 性能优化建议

1. **批量大小调整**：根据服务器性能调整 `batch_size`
2. **并发控制**：避免同时运行多个增量任务
3. **数据库优化**：确保主键字段有索引
4. **API 配额管理**：合理安排任务时间，避免超出 API 限制

## 扩展开发

如需添加新的数据源，可以：

1. 继承 `IncrementalProcessor` 基类
2. 实现 `process_incremental_data` 方法
3. 在调度器中注册新任务

示例：
```python
class NewDataProcessor(IncrementalProcessor):
    def process_incremental_data(self, start_date: str, end_date: str = None) -> int:
        # 实现具体的增量处理逻辑
        pass
```

## 常见问题

### Q: 如何处理数据回填？
A: 使用 `--start-date` 参数指定较早的日期即可。

### Q: 增量更新会覆盖现有数据吗？
A: 会的，使用 UPSERT 操作，相同主键的数据会被更新。

### Q: 如何暂停某个任务？
A: 修改调度器中对应任务的 `enabled` 字段为 `False`。

### Q: API 限频怎么办？
A: 系统有自动重试机制，如果频繁限频，可以调整 `max_requests_per_minute` 参数。

### Q: 数据不完整怎么办？
A: 检查 `safe_lag_days` 设置，增加安全延迟天数。
