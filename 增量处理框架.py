import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, DateTime, Integer, Float
from datetime import datetime, timedelta
import logging
import json
import time
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"

# Initialize Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('incremental_update.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IncrementalProcessor(ABC):
    """增量处理基类"""
    
    def __init__(self, database_url: str = DATABASE_URL):
        self.engine = create_engine(database_url)
        self.metadata = MetaData()
        self.watermark_table = 'incremental_watermarks'
        self._ensure_watermark_table()
    
    def _ensure_watermark_table(self):
        """确保水位线表存在"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text(f"""
                    CREATE TABLE IF NOT EXISTS {self.watermark_table} (
                        job_name VARCHAR(100) PRIMARY KEY,
                        last_update_time DATETIME,
                        last_trade_date VARCHAR(8),
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        status VARCHAR(20) DEFAULT 'SUCCESS',
                        records_processed INT DEFAULT 0,
                        error_message TEXT
                    )
                """))
                conn.commit()
                logger.info(f"Watermark table {self.watermark_table} ensured")
        except Exception as e:
            logger.error(f"Error creating watermark table: {e}")
            raise
    
    def get_last_watermark(self, job_name: str) -> Optional[Dict[str, Any]]:
        """获取上次处理的水位线"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(f"""
                    SELECT last_update_time, last_trade_date, status, records_processed
                    FROM {self.watermark_table} 
                    WHERE job_name = :job_name
                """), {"job_name": job_name})
                
                row = result.fetchone()
                if row:
                    return {
                        'last_update_time': row[0],
                        'last_trade_date': row[1],
                        'status': row[2],
                        'records_processed': row[3]
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting watermark for {job_name}: {e}")
            return None
    
    def update_watermark(self, job_name: str, last_trade_date: str, 
                        records_processed: int = 0, status: str = 'SUCCESS', 
                        error_message: str = None):
        """更新水位线"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text(f"""
                    INSERT INTO {self.watermark_table} 
                    (job_name, last_update_time, last_trade_date, records_processed, status, error_message)
                    VALUES (:job_name, :update_time, :trade_date, :records, :status, :error)
                    ON DUPLICATE KEY UPDATE
                    last_update_time = :update_time,
                    last_trade_date = :trade_date,
                    updated_at = CURRENT_TIMESTAMP,
                    records_processed = :records,
                    status = :status,
                    error_message = :error
                """), {
                    "job_name": job_name,
                    "update_time": datetime.now(),
                    "trade_date": last_trade_date,
                    "records": records_processed,
                    "status": status,
                    "error": error_message
                })
                conn.commit()
                logger.info(f"Updated watermark for {job_name}: {last_trade_date}")
        except Exception as e:
            logger.error(f"Error updating watermark for {job_name}: {e}")
            raise
    
    def get_trading_dates_since(self, start_date: str, end_date: str = None) -> List[str]:
        """获取指定日期范围内的交易日"""
        try:
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 获取交易日历
            cal_df = pro.trade_cal(exchange='SSE', start_date=start_date, end_date=end_date)
            trading_dates = cal_df[cal_df['is_open'] == 1]['cal_date'].tolist()
            return trading_dates
        except Exception as e:
            logger.error(f"Error getting trading dates: {e}")
            return []
    
    @abstractmethod
    def process_incremental_data(self, start_date: str, end_date: str = None) -> int:
        """处理增量数据的抽象方法"""
        pass
    
    def run_incremental_update(self, job_name: str, force_start_date: str = None, 
                              end_date: str = None, safe_lag_days: int = 1):
        """运行增量更新"""
        logger.info(f"Starting incremental update for job: {job_name}")
        
        try:
            # 获取上次水位线
            watermark = self.get_last_watermark(job_name)
            
            # 确定开始日期
            if force_start_date:
                start_date = force_start_date
                logger.info(f"Using forced start date: {start_date}")
            elif watermark and watermark['last_trade_date']:
                # 从上次处理日期的下一个交易日开始
                last_date = datetime.strptime(watermark['last_trade_date'], '%Y%m%d')
                start_date = (last_date + timedelta(days=1)).strftime('%Y%m%d')
                logger.info(f"Using watermark start date: {start_date}")
            else:
                # 首次运行，从30天前开始
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
                logger.info(f"First run, using default start date: {start_date}")
            
            # 确定结束日期（考虑安全延迟）
            if end_date is None:
                end_date = (datetime.now() - timedelta(days=safe_lag_days)).strftime('%Y%m%d')
            
            logger.info(f"Processing data from {start_date} to {end_date}")
            
            # 处理增量数据
            records_processed = self.process_incremental_data(start_date, end_date)
            
            # 更新水位线
            self.update_watermark(job_name, end_date, records_processed, 'SUCCESS')
            
            logger.info(f"Incremental update completed. Processed {records_processed} records")
            return records_processed
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Incremental update failed for {job_name}: {error_msg}")
            
            # 记录失败状态
            try:
                self.update_watermark(job_name, start_date if 'start_date' in locals() else '', 
                                    0, 'FAILED', error_msg)
            except:
                pass
            
            raise

class RateLimiter:
    """API调用频率限制器"""
    
    def __init__(self, max_requests_per_minute: int = 950):
        self.max_requests = max_requests_per_minute
        self.request_count = 0
        self.start_time = time.time()
        self.last_request_time = 0
    
    def wait_if_needed(self):
        """如果需要则等待"""
        current_time = time.time()
        
        # 如果过了一分钟，重置计数器
        if current_time - self.start_time >= 60:
            self.request_count = 0
            self.start_time = current_time
        
        # 如果接近限制，等待
        if self.request_count >= self.max_requests:
            elapsed = current_time - self.start_time
            if elapsed < 60:
                wait_time = 60 - elapsed + 1
                logger.info(f"Rate limit reached, waiting {wait_time:.1f} seconds...")
                time.sleep(wait_time)
                self.request_count = 0
                self.start_time = time.time()
        
        # 请求间添加小延迟
        time_since_last = current_time - self.last_request_time
        if time_since_last < 0.1:
            time.sleep(0.1 - time_since_last)
        
        self.last_request_time = time.time()
    
    def increment(self):
        """增加请求计数"""
        self.request_count += 1

def api_call_with_retry(func, *args, max_retries: int = 3, rate_limiter: RateLimiter = None, **kwargs):
    """带重试的API调用"""
    for attempt in range(max_retries):
        try:
            if rate_limiter:
                rate_limiter.wait_if_needed()
            
            result = func(*args, **kwargs)
            
            if rate_limiter:
                rate_limiter.increment()
            
            return result
            
        except Exception as e:
            error_msg = str(e).lower()
            
            if "1000 times per minute" in error_msg or "rate limit" in error_msg:
                if attempt < max_retries - 1:
                    delay = (2 ** attempt) + 1
                    logger.warning(f"Rate limit hit, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"Rate limit exceeded after {max_retries} attempts")
                    raise
            else:
                if attempt < max_retries - 1:
                    delay = 0.5 * (attempt + 1)
                    logger.warning(f"API error: {e}, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"API call failed after {max_retries} attempts: {e}")
                    raise
    
    return None
